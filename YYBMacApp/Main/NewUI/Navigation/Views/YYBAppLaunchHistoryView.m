//
//  YYBAppLaunchHistoryView.m
//  YYBMacApp
//
//  Created by lichenlin on 2025/8/20.
//

#import "YYBAppLaunchHistoryView.h"
#import "InstallApkInfo.h"
#import "SDWebImage.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "Masonry.h"
#import "YYBApkPackage.h"
#import "MainUIDefine.h"


static const CGFloat kItemHeight = 40.0;  // 每个展开的条目高度
static const CGFloat kItemSpacing = 8.0;  // 每个条目之间间距
static const CGFloat kIconSize = 28.0;  // 图标大小
static const CGFloat kFixedHeight = 164.0;  // view固定高度
static const CGFloat kTextLabelSpacing = 16.0;  // 文本标签与app item间隔

// 动画时长
static const NSTimeInterval kAnimationDuration = 0.25;

static NSString *const kTag = @"YYBAppLaunchHistoryView";

// 定义历史视图状态枚举
typedef NS_ENUM(NSUInteger, YYBAppLaunchHistoryViewState) {
    YYBAppLaunchHistoryViewStateNormal,   // 正常状态
    YYBAppLaunchHistoryViewStateHover,    // 鼠标悬停状态
};

// 按钮毛玻璃效果
static NSVisualEffectMaterial const historyItemStateNormal = NSVisualEffectMaterialUnderPageBackground;
static NSVisualEffectMaterial const historyItemStateHover = NSVisualEffectMaterialMenu;

// 自定义的可点击 item view
@interface YYBAppLaunchHistoryItemView : NSView
@property (nonatomic, strong) InstallApkInfo *apkInfo;
@property (nonatomic, assign) BOOL expanded;
@property (nonatomic, strong) NSVisualEffectView *containerView;
@property (nonatomic, strong) NSImageView *iconImageView;
@property (nonatomic, strong) NSTextField *nameLabel;
@property (nonatomic, strong) NSTrackingArea *trackingArea;
@property (nonatomic, assign) YYBAppLaunchHistoryViewState viewState;
@end

@implementation YYBAppLaunchHistoryItemView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    // 设置初始状态
    self.viewState = YYBAppLaunchHistoryViewStateNormal;

    // 创建容器视图
    self.containerView = [[NSVisualEffectView alloc] init];
    self.containerView.wantsLayer = YES;
    self.containerView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    self.containerView.state = NSVisualEffectStateActive;
    self.containerView.layer.cornerRadius = 8.0;
    self.containerView.layer.masksToBounds = YES;
    self.containerView.material = historyItemStateNormal;
    [self addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

    // 创建图标视图
    self.iconImageView = [[NSImageView alloc] init];
    self.iconImageView.imageScaling = NSImageScaleProportionallyUpOrDown;
    self.iconImageView.wantsLayer = YES;
    self.iconImageView.layer.cornerRadius = 4.0;
    self.iconImageView.layer.masksToBounds = YES;
    [self.containerView addSubview:self.iconImageView];

    // 创建应用名称标签
    self.nameLabel = [[NSTextField alloc] init];
    self.nameLabel.font = [NSFont systemFontOfSize:12];
    self.nameLabel.textColor = [NSColor whiteColor];
    self.nameLabel.backgroundColor = [NSColor clearColor];
    self.nameLabel.bordered = NO;
    self.nameLabel.editable = NO;
    self.nameLabel.selectable = NO;
    self.nameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    self.nameLabel.maximumNumberOfLines = 1;
    self.nameLabel.alignment = NSTextAlignmentLeft;
    [self.nameLabel setAlphaValue:0]; // 初始透明
    [self.containerView addSubview:self.nameLabel];

    // 添加点击事件
    NSClickGestureRecognizer *clickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(handleClick:)];
    [self addGestureRecognizer:clickGesture];
}

- (void)setFrameSize:(NSSize)newSize {
    [super setFrameSize:newSize];

    // 计算文字透明度，仿照 YYBMainNavigationItemView 的实现
    if (self.expanded) {
        // 展开状态下，根据宽度变化计算透明度
        CGFloat minWidth = kIconSize + 16; // 图标宽度 + 左右边距
        CGFloat maxWidth = kNavigationExpandWidth; // 使用导航栏的展开宽度作为参考
        CGFloat alpha = (newSize.width - minWidth) / (maxWidth - minWidth);
        alpha = MAX(0.0, MIN(1.0, alpha)); // 限制在 0-1 之间
        self.nameLabel.alphaValue = alpha;
    } else {
        self.nameLabel.alphaValue = 0;
    }
}

- (void)updateTrackingAreas {
    [super updateTrackingAreas];

    if (self.trackingArea) {
        [self removeTrackingArea:self.trackingArea];
    }

    NSTrackingAreaOptions options = NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingMouseMoved;
    self.trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                     options:options
                                                       owner:self
                                                    userInfo:nil];
    [self addTrackingArea:self.trackingArea];
}

- (void)setExpanded:(BOOL)expanded {
    if (_expanded != expanded) {
        _expanded = expanded;
        [self updateLayout];
        [self updateViewWithState:self.viewState];
    }
}

- (void)setApkInfo:(InstallApkInfo *)apkInfo {
    _apkInfo = apkInfo;
    [self updateContent];
}

- (void)updateContent {
    if (!self.apkInfo) {
        return;
    }

    // 设置应用名称
    self.nameLabel.stringValue = self.apkInfo.name ?: @"";

    // 设置图标
    [self.iconImageView sd_setImageWithURL:[NSURL URLWithString:self.apkInfo.iconUrl]];
}

- (void)updateLayout {
    // 更新图标约束
    [self.iconImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (self.expanded) {
            make.left.equalTo(self.containerView).offset(8);
            make.centerY.equalTo(self.containerView);
            make.width.height.equalTo(@(kIconSize));
        } else {
            make.center.equalTo(self.containerView);
            make.width.height.equalTo(@(kIconSize));
        }
    }];

    // 更新文字约束
    if (self.expanded) {
        self.nameLabel.hidden = NO;
        [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.iconImageView.mas_right).offset(8);
            make.right.equalTo(self.containerView).offset(-8);
            make.centerY.equalTo(self.containerView);
        }];
    } else {
        self.nameLabel.hidden = YES;
    }
}

// 更新视图状态
- (void)updateViewWithState:(YYBAppLaunchHistoryViewState)state {
    self.viewState = state;

    [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
        context.duration = kAnimationDuration;
        context.allowsImplicitAnimation = YES;

        switch (state) {
            case YYBAppLaunchHistoryViewStateNormal:
                self.containerView.material = historyItemStateNormal;
                break;

            case YYBAppLaunchHistoryViewStateHover:
                self.containerView.material = historyItemStateHover;
                break;
        }
    }];
}

- (void)handleClick:(NSClickGestureRecognizer *)gesture {
    if (self.apkInfo && self.apkInfo.pkgName) {
        YYBMacLogInfo(kTag, @"点击打开APP: %@", self.apkInfo.name);
        [[YYBApkPackage shared] openApp:self.apkInfo.pkgName];
    }
}

- (void)mouseEntered:(NSEvent *)event {
    [super mouseEntered:event];
    [self updateViewWithState:YYBAppLaunchHistoryViewStateHover];
}

- (void)mouseExited:(NSEvent *)event {
    [super mouseExited:event];
    [self updateViewWithState:YYBAppLaunchHistoryViewStateNormal];
}

@end

@interface YYBAppLaunchHistoryView ()

@property (nonatomic, strong) NSTextField *titleLabel;  // 标题标签
@property (nonatomic, strong) NSMutableArray<YYBAppLaunchHistoryItemView *> *itemViews;

@end


@implementation YYBAppLaunchHistoryView


- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        _expanded = YES; // 默认展开状态
        _itemViews = [NSMutableArray array];
        [self setupView];
    }
    return self;
}


- (void)setupView {
    self.wantsLayer = YES;
    self.layer.backgroundColor = [NSColor clearColor].CGColor;

    [self setupTitleLabel];
    [self updateLayout];
}

- (void)setupTitleLabel {
    self.titleLabel = [[NSTextField alloc] init];
    self.titleLabel.stringValue = self.expanded ? @"快捷启动" : @"启动";
    self.titleLabel.font = [NSFont systemFontOfSize:12 weight:NSFontWeightRegular];
    self.titleLabel.textColor = [[NSColor whiteColor] colorWithAlphaComponent:0.45];
    self.titleLabel.backgroundColor = [NSColor clearColor];
    self.titleLabel.bordered = NO;
    self.titleLabel.editable = NO;
    self.titleLabel.selectable = NO;
    self.titleLabel.alignment = NSTextAlignmentCenter;

    [self addSubview:self.titleLabel];
}


// 切换展开和收起
- (void)setExpanded:(BOOL)expanded {
    if (_expanded != expanded) {
        _expanded = expanded;

        // 使用动画更新标题文本和布局
        [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
            context.duration = kAnimationDuration;
            context.allowsImplicitAnimation = YES;

            // 更新标题文本
            self.titleLabel.stringValue = self.expanded ? @"快捷启动" : @"启动";

            // 重新布局、更新界面
            [self updateLayout];
        }];
    }
}

- (void)updateWithRecentApps:(NSArray<InstallApkInfo *> *)recentApps {
    // 清除旧的视图
    for (NSView *view in self.itemViews) {
        [view removeFromSuperview];
    }
    [self.itemViews removeAllObjects];

    // 最多显示3个
    NSInteger count = MIN(recentApps.count, 3);

    // 如果没有app item，隐藏整个view
    if (count == 0) {
        self.hidden = YES;
        [self updateLayout];
        return;
    } else {
        self.hidden = NO;
    }

    // 创建新的视图
    for (NSInteger i = 0; i < count; i++) {
        InstallApkInfo *apkInfo = recentApps[i];
        YYBMacLogInfo(kTag, @"刷新历史记录显示，获取到第 %ld 个最近使用的APP: %@", (long)(i+1), apkInfo.name);
        YYBAppLaunchHistoryItemView *itemView = [self createItemViewForApkInfo:apkInfo];

        [self addSubview:itemView];
        [self.itemViews addObject:itemView];
    }

    [self updateLayout];
}


- (YYBAppLaunchHistoryItemView *)createItemViewForApkInfo:(InstallApkInfo *)apkInfo {
    YYBAppLaunchHistoryItemView *itemView = [[YYBAppLaunchHistoryItemView alloc] init];
    itemView.apkInfo = apkInfo;
    itemView.expanded = self.expanded;

    return itemView;
}


- (void)updateLayout {

    if(self.itemViews.count == 0) {
        // view消失 - 只更新高度约束，不要清除其他约束。防止刷新向左偏移
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@0);
        }];
        return;
    }

    // 固定整个view的高度为164 - 只更新高度约束，不要清除其他约束。防止刷新向左偏移
    [self mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@(kFixedHeight));
    }];

    // 布局标题标签
    [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self);
        make.height.equalTo(@20); // 标题标签高度
    }];



    CGFloat itemHeight = self.expanded ? kItemHeight : kIconSize + 8;
    CGFloat startY = 20 + kTextLabelSpacing; // 标题高度 + 间隔

    // 布局app item，向上对齐
    for (NSInteger i = 0; i < self.itemViews.count; i++) {
        YYBAppLaunchHistoryItemView *itemView = self.itemViews[i];
        [itemView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self);
            make.height.equalTo(@(itemHeight));
            make.top.equalTo(self).offset(startY + i * (itemHeight + kItemSpacing));
        }];
    }

    [self updateItemViewsForExpandedState];
}


- (void)updateItemViewsForExpandedState {
    // 使用动画更新所有 item view 的展开状态
    [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
        context.duration = kAnimationDuration;
        context.allowsImplicitAnimation = YES;

        for (YYBAppLaunchHistoryItemView *itemView in self.itemViews) {
            itemView.expanded = self.expanded;
        }
    }];
}


@end
